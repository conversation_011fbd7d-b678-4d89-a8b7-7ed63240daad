import { ID } from 'appwrite';
import { account, appwriteConfig, databases, Query } from '../appwrite';

export interface User {
  $id: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  avatar?: string;
}

export interface CreateUserAccount {
  email: string;
  password: string;
  name: string;
  phone?: string;
}

class AuthService {
  // Create a new user account
  async createAccount({ email, password, name, phone }: CreateUserAccount): Promise<User> {
    try {
      const newAccount = await account.create(
        ID.unique(),
        email,
        password,
        name
      );

      if (!newAccount) throw new Error('Failed to create account');

      // Create user document in database
      const newUser = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.usersCollectionId,
        ID.unique(),
        {
          accountId: newAccount.$id,
          email: newAccount.email,
          name: newAccount.name,
          phone: phone || '',
          address: '',
          avatar: '',
        }
      );

      return newUser as unknown as User;
    } catch (error) {
      console.error('Error creating account:', error);
      throw error;
    }
  }

  // Sign in user
  async signIn(email: string, password: string) {
    try {
      const session = await account.createEmailPasswordSession(email, password);
      return session;
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const currentAccount = await account.get();
      if (!currentAccount) return null;

      const currentUser = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.usersCollectionId,
        [Query.equal('accountId', currentAccount.$id)]
      );

      if (!currentUser.documents.length) return null;

      return currentUser.documents[0] as unknown as User;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Sign out user
  async signOut() {
    try {
      const session = await account.deleteSession('current');
      return session;
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  // Update user profile
  async updateProfile(userId: string, updates: Partial<Omit<User, '$id'>>) {
    try {
      const updatedUser = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.usersCollectionId,
        userId,
        updates
      );
      return updatedUser as unknown as User;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  }
}

export default new AuthService();
