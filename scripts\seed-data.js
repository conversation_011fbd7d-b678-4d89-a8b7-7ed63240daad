const { Client, Databases, ID } = require('appwrite');

// Initialize Appwrite client
const client = new Client();
client
  .setEndpoint('https://fra.cloud.appwrite.io/v1')
  .setProject('689dc689002da847e62d')
  .setKey('YOUR_API_KEY_HERE'); // You'll need to add your API key

const databases = new Databases(client);
const databaseId = 'jujafresh-db';

// Sample categories data
const categories = [
  {
    name: 'Fruits',
    icon: 'leaf.fill',
    color: '#4CAF50',
    description: 'Fresh fruits and seasonal produce'
  },
  {
    name: 'Vegetables',
    icon: 'carrot.fill',
    color: '#8BC34A',
    description: 'Fresh vegetables and greens'
  },
  {
    name: 'Dairy',
    icon: 'drop.fill',
    color: '#2196F3',
    description: 'Milk, cheese, yogurt and dairy products'
  },
  {
    name: 'Meat & Poultry',
    icon: 'flame.fill',
    color: '#FF5722',
    description: 'Fresh meat, chicken and seafood'
  },
  {
    name: 'Bakery',
    icon: 'birthday.cake.fill',
    color: '#FF9800',
    description: 'Fresh bread, cakes and baked goods'
  },
  {
    name: 'Pantry',
    icon: 'archivebox.fill',
    color: '#795548',
    description: 'Rice, flour, spices and cooking essentials'
  }
];

// Sample products data
const products = [
  // Fruits
  {
    name: 'Fresh Bananas',
    description: 'Sweet and ripe bananas, perfect for snacking or smoothies',
    price: 150,
    unit: 'kg',
    category: 'Fruits',
    image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',
    inStock: true,
    stockQuantity: 50,
    discount: 20,
    featured: true
  },
  {
    name: 'Red Apples',
    description: 'Crisp and juicy red apples, rich in vitamins',
    price: 200,
    unit: 'kg',
    category: 'Fruits',
    image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400',
    inStock: true,
    stockQuantity: 30,
    featured: true
  },
  {
    name: 'Fresh Oranges',
    description: 'Juicy oranges packed with vitamin C',
    price: 180,
    unit: 'kg',
    category: 'Fruits',
    image: 'https://images.unsplash.com/photo-1547514701-42782101795e?w=400',
    inStock: true,
    stockQuantity: 40,
    featured: false
  },
  {
    name: 'Avocados',
    description: 'Creamy avocados perfect for salads and toast',
    price: 300,
    unit: 'kg',
    category: 'Fruits',
    image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400',
    inStock: true,
    stockQuantity: 25,
    featured: true
  },

  // Vegetables
  {
    name: 'Fresh Tomatoes',
    description: 'Ripe and juicy tomatoes for cooking and salads',
    price: 80,
    unit: 'kg',
    category: 'Vegetables',
    image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=400',
    inStock: true,
    stockQuantity: 60,
    featured: true
  },
  {
    name: 'Green Spinach',
    description: 'Fresh spinach leaves rich in iron and vitamins',
    price: 60,
    unit: 'bunch',
    category: 'Vegetables',
    image: 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400',
    inStock: true,
    stockQuantity: 35,
    featured: false
  },
  {
    name: 'Carrots',
    description: 'Fresh carrots perfect for cooking and snacking',
    price: 70,
    unit: 'kg',
    category: 'Vegetables',
    image: 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400',
    inStock: true,
    stockQuantity: 45,
    featured: false
  },

  // Dairy
  {
    name: 'Whole Milk',
    description: 'Fresh whole milk from local farms',
    price: 120,
    unit: 'liter',
    category: 'Dairy',
    image: 'https://images.unsplash.com/photo-1563636619-e9143da7973b?w=400',
    inStock: true,
    stockQuantity: 20,
    featured: true
  },
  {
    name: 'Greek Yogurt',
    description: 'Creamy Greek yogurt with probiotics',
    price: 250,
    unit: '500g',
    category: 'Dairy',
    image: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400',
    inStock: true,
    stockQuantity: 15,
    featured: false
  },
  {
    name: 'Cheddar Cheese',
    description: 'Aged cheddar cheese for cooking and snacking',
    price: 400,
    unit: '250g',
    category: 'Dairy',
    image: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=400',
    inStock: true,
    stockQuantity: 12,
    featured: false
  },

  // Bakery
  {
    name: 'Brown Bread',
    description: 'Freshly baked whole wheat bread',
    price: 90,
    unit: 'loaf',
    category: 'Bakery',
    image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',
    inStock: true,
    stockQuantity: 25,
    featured: true
  },
  {
    name: 'Croissants',
    description: 'Buttery and flaky croissants',
    price: 150,
    unit: '4 pieces',
    category: 'Bakery',
    image: 'https://images.unsplash.com/photo-1555507036-ab794f4afe5e?w=400',
    inStock: true,
    stockQuantity: 18,
    featured: false
  },

  // Meat & Poultry
  {
    name: 'Chicken Breast',
    description: 'Fresh boneless chicken breast',
    price: 450,
    unit: 'kg',
    category: 'Meat & Poultry',
    image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',
    inStock: true,
    stockQuantity: 20,
    featured: false
  },
  {
    name: 'Ground Beef',
    description: 'Fresh ground beef for cooking',
    price: 600,
    unit: 'kg',
    category: 'Meat & Poultry',
    image: 'https://images.unsplash.com/photo-1588347818133-6b2e6d8b1c0e?w=400',
    inStock: true,
    stockQuantity: 15,
    featured: false
  },

  // Pantry
  {
    name: 'Basmati Rice',
    description: 'Premium basmati rice for cooking',
    price: 180,
    unit: 'kg',
    category: 'Pantry',
    image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400',
    inStock: true,
    stockQuantity: 50,
    featured: false
  },
  {
    name: 'Cooking Oil',
    description: 'Pure sunflower cooking oil',
    price: 220,
    unit: 'liter',
    category: 'Pantry',
    image: 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400',
    inStock: true,
    stockQuantity: 30,
    featured: false
  }
];

async function seedData() {
  try {
    console.log('Seeding JujaFresh database with sample data...');

    // Seed categories
    console.log('Adding categories...');
    const categoryIds = {};
    for (const category of categories) {
      try {
        const doc = await databases.createDocument(
          databaseId,
          'categories',
          ID.unique(),
          category
        );
        categoryIds[category.name] = doc.$id;
        console.log(`✅ Added category: ${category.name}`);
      } catch (error) {
        console.log(`⚠️ Category ${category.name} might already exist`);
      }
    }

    // Seed products
    console.log('Adding products...');
    for (const product of products) {
      try {
        const productData = {
          ...product,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        const doc = await databases.createDocument(
          databaseId,
          'products',
          ID.unique(),
          productData
        );
        console.log(`✅ Added product: ${product.name}`);
      } catch (error) {
        console.log(`⚠️ Product ${product.name} might already exist or there's an error:`, error.message);
      }
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log('\nSample data added:');
    console.log(`- ${categories.length} categories`);
    console.log(`- ${products.length} products`);
    console.log('\nYour JujaFresh app is now ready to use!');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
}

seedData();