import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, TextInput, TouchableOpacity } from 'react-native';

const popularSearches = ['Milk', 'Bread', 'Eggs', 'Bananas', 'Rice', 'Chicken', 'Tomatoes', 'Onions'];

const recentSearches = ['Fresh vegetables', 'Organic milk', 'Whole wheat bread'];

export default function SearchScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const colorScheme = useColorScheme();
  
  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedView style={styles.searchContainer}>
          <IconSymbol name="magnifyingglass" size={20} color="#666" />
          <TextInput
            style={[styles.searchInput, { 
              color: colorScheme === 'dark' ? '#fff' : '#000',
            }]}
            placeholder="Search for products..."
            placeholderTextColor="#666"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <IconSymbol name="xmark.circle.fill" size={20} color="#666" />
            </TouchableOpacity>
          )}
        </ThemedView>
      </ThemedView>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {searchQuery.length === 0 && (
          <>
            <ThemedView style={styles.section}>
              <ThemedText type="subtitle">Recent Searches</ThemedText>
              {recentSearches.map((search, index) => (
                <TouchableOpacity 
                  key={index} 
                  style={styles.searchItem}
                  onPress={() => setSearchQuery(search)}
                >
                  <IconSymbol name="clock" size={16} color="#666" />
                  <ThemedText style={styles.searchText}>{search}</ThemedText>
                </TouchableOpacity>
              ))}
            </ThemedView>

            <ThemedView style={styles.section}>
              <ThemedText type="subtitle">Popular Searches</ThemedText>
              <ThemedView style={styles.tagsContainer}>
                {popularSearches.map((tag, index) => (
                  <TouchableOpacity 
                    key={index} 
                    style={styles.tag}
                    onPress={() => setSearchQuery(tag)}
                  >
                    <ThemedText style={styles.tagText}>{tag}</ThemedText>
                  </TouchableOpacity>
                ))}
              </ThemedView>
            </ThemedView>
          </>
        )}

        {searchQuery.length > 0 && (
          <ThemedView style={styles.section}>
            <ThemedText type="subtitle">Search Results for "{searchQuery}"</ThemedText>
            <ThemedView style={styles.resultsContainer}>
              <ThemedText style={styles.noResults}>
                {searchQuery.length < 3 
                  ? 'Type at least 3 characters to search...' 
                  : 'No products found. Try a different search term.'}
              </ThemedText>
            </ThemedView>
          </ThemedView>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 20,
  },
  searchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12,
  },
  searchText: {
    fontSize: 16,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 12,
  },
  tag: {
    backgroundColor: 'rgba(0,0,0,0.1)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  tagText: {
    fontSize: 14,
  },
  resultsContainer: {
    marginTop: 20,
    padding: 20,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 12,
  },
  noResults: {
    textAlign: 'center',
    opacity: 0.6,
    fontSize: 16,
  },
});