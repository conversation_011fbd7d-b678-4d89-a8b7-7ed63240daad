import { BorderRadius, Colors, Spacing, Typography } from "@/constants/Colors";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function CheckoutScreen() {
  const [shippingInfo, setShippingInfo] = useState({
    fullName: "",
    address: "",
    city: "",
    zipCode: "",
    phone: "",
  });

  const [paymentInfo, setPaymentInfo] = useState({
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    cardholderName: "",
  });

  const [orderSummary] = useState({
    subtotal: 299.99,
    shipping: 9.99,
    tax: 24.0,
    total: 333.98,
  });

  const [isLoading, setIsLoading] = useState(false);

  const handlePlaceOrder = async () => {
    // Validate required fields
    if (
      !shippingInfo.fullName ||
      !shippingInfo.address ||
      !paymentInfo.cardNumber
    ) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      Alert.alert(
        "Order Placed!",
        "Your order has been successfully placed. You will receive a confirmation email shortly.",
        [{ text: "OK", onPress: () => router.push("/(tabs)") }]
      );
    }, 2000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={styles.backButton}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Checkout</Text>
        </View>

        {/* Shipping Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Shipping Information</Text>

          <TextInput
            style={styles.input}
            placeholder="Full Name *"
            value={shippingInfo.fullName}
            onChangeText={(text) =>
              setShippingInfo({ ...shippingInfo, fullName: text })
            }
          />

          <TextInput
            style={styles.input}
            placeholder="Address *"
            value={shippingInfo.address}
            onChangeText={(text) =>
              setShippingInfo({ ...shippingInfo, address: text })
            }
          />

          <View style={styles.row}>
            <TextInput
              style={[styles.input, styles.halfInput]}
              placeholder="City"
              value={shippingInfo.city}
              onChangeText={(text) =>
                setShippingInfo({ ...shippingInfo, city: text })
              }
            />
            <TextInput
              style={[styles.input, styles.halfInput]}
              placeholder="ZIP Code"
              value={shippingInfo.zipCode}
              onChangeText={(text) =>
                setShippingInfo({ ...shippingInfo, zipCode: text })
              }
            />
          </View>

          <TextInput
            style={styles.input}
            placeholder="Phone Number"
            value={shippingInfo.phone}
            onChangeText={(text) =>
              setShippingInfo({ ...shippingInfo, phone: text })
            }
            keyboardType="phone-pad"
          />
        </View>

        {/* Payment Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Information</Text>

          <TextInput
            style={styles.input}
            placeholder="Card Number *"
            value={paymentInfo.cardNumber}
            onChangeText={(text) =>
              setPaymentInfo({ ...paymentInfo, cardNumber: text })
            }
            keyboardType="numeric"
            maxLength={16}
          />

          <TextInput
            style={styles.input}
            placeholder="Cardholder Name"
            value={paymentInfo.cardholderName}
            onChangeText={(text) =>
              setPaymentInfo({ ...paymentInfo, cardholderName: text })
            }
          />

          <View style={styles.row}>
            <TextInput
              style={[styles.input, styles.halfInput]}
              placeholder="MM/YY"
              value={paymentInfo.expiryDate}
              onChangeText={(text) =>
                setPaymentInfo({ ...paymentInfo, expiryDate: text })
              }
              maxLength={5}
            />
            <TextInput
              style={[styles.input, styles.halfInput]}
              placeholder="CVV"
              value={paymentInfo.cvv}
              onChangeText={(text) =>
                setPaymentInfo({ ...paymentInfo, cvv: text })
              }
              keyboardType="numeric"
              maxLength={3}
              secureTextEntry
            />
          </View>
        </View>

        {/* Order Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Order Summary</Text>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Subtotal</Text>
            <Text style={styles.summaryValue}>
              ${orderSummary.subtotal.toFixed(2)}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Shipping</Text>
            <Text style={styles.summaryValue}>
              ${orderSummary.shipping.toFixed(2)}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Tax</Text>
            <Text style={styles.summaryValue}>
              ${orderSummary.tax.toFixed(2)}
            </Text>
          </View>

          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>
              ${orderSummary.total.toFixed(2)}
            </Text>
          </View>
        </View>

        {/* Place Order Button */}
        <TouchableOpacity
          style={styles.placeOrderButton}
          activeOpacity={0.9}
          onPress={handlePlaceOrder}
        >
          <Text style={styles.placeOrderText}>
            {isLoading ? 'Processing...' : 'Place Order'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    marginBottom: 20,
  },
  backButton: {
    fontSize: 16,
    color: Colors.light.tint,
    marginRight: 20,
  },
  title: {
    fontSize: Typography.fontSizes['2xl'],
    fontWeight: Typography.fontWeights.bold,
    color: Colors.light.text,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: Typography.fontSizes.lg,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.light.text,
    marginBottom: Spacing.lg,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.lg,
    fontSize: Typography.fontSizes.base,
    marginBottom: Spacing.lg,
    backgroundColor: Colors.light.surface,
    fontWeight: Typography.fontWeights.normal,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  halfInput: {
    width: "48%",
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: "#666",
  },
  summaryValue: {
    fontSize: 16,
    color: "#333",
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: "#ddd",
    marginTop: 10,
    paddingTop: 15,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.light.tint,
  },
  placeOrderButton: {
    backgroundColor: Colors.light.tint,
    paddingVertical: 18,
    borderRadius: 12,
    alignItems: "center",
    marginVertical: 20,
  },
  placeOrderText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
  },
});
