#!/usr/bin/env node

const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚀 JujaFresh Appwrite Configuration');
console.log('===================================\n');

console.log('This script will help you configure your Appwrite project.\n');

console.log('Before running this script, make sure you have:');
console.log('1. Created an Appwrite project at https://cloud.appwrite.io');
console.log('2. Copied your project ID from the project settings');
console.log('3. Your project ID ready to paste\n');

rl.question('Enter your Appwrite Project ID: ', (projectId) => {
  if (!projectId || projectId.trim().length === 0) {
    console.log('❌ Project ID cannot be empty');
    rl.close();
    return;
  }

  // Update .env file
  const envPath = path.join(__dirname, '..', '.env');
  let envContent = '';
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
    envContent = envContent.replace(
      /EXPO_PUBLIC_APPWRITE_PROJECT_ID=.*/,
      `EXPO_PUBLIC_APPWRITE_PROJECT_ID=${projectId.trim()}`
    );
  } else {
    envContent = `# Appwrite Configuration
EXPO_PUBLIC_APPWRITE_ENDPOINT=https://fra.cloud.appwrite.io/v1
EXPO_PUBLIC_APPWRITE_PROJECT_ID=${projectId.trim()}
EXPO_PUBLIC_APPWRITE_DATABASE_ID=jujafresh-db
EXPO_PUBLIC_APPWRITE_STORAGE_ID=product-images

# Collection IDs
EXPO_PUBLIC_USERS_COLLECTION_ID=users
EXPO_PUBLIC_PRODUCTS_COLLECTION_ID=products
EXPO_PUBLIC_CATEGORIES_COLLECTION_ID=categories
EXPO_PUBLIC_ORDERS_COLLECTION_ID=orders
EXPO_PUBLIC_CART_COLLECTION_ID=cart`;
  }

  fs.writeFileSync(envPath, envContent);
  console.log('✅ Project ID configured successfully');

  rl.question('Do you want to set up the database structure now? (y/n): ', (setupDb) => {
    if (setupDb.toLowerCase() === 'y') {
      console.log('\n📋 To set up your database:');
      console.log('1. Go to https://cloud.appwrite.io');
      console.log('2. Open your project');
      console.log('3. Go to Settings > API Keys');
      console.log('4. Create a new API key with these scopes:');
      console.log('   - databases.read, databases.write');
      console.log('   - collections.read, collections.write');
      console.log('   - attributes.read, attributes.write');
      console.log('   - documents.read, documents.write');
      console.log('5. Run: node scripts/setup.js');
      console.log('\n🎉 Configuration completed!');
    } else {
      console.log('\n🎉 Configuration completed!');
      console.log('\nNext steps:');
      console.log('1. Run: npm start');
      console.log('2. Open the app on your device/simulator');
      console.log('\nNote: You may need to set up the database structure later.');
    }
    rl.close();
  });
});