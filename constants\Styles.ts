import { StyleSheet } from 'react-native';
import { BorderRadius, Colors, Spacing, Typography } from './Colors';

/**
 * Shared styles for consistent UI across the app
 */
export const SharedStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  
  safeContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },

  // Header styles
  header: {
    paddingHorizontal: Spacing.xl,
    paddingTop: 60,
    paddingBottom: Spacing.xl,
  },

  headerWithBorder: {
    paddingHorizontal: Spacing.xl,
    paddingTop: 60,
    paddingBottom: Spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.borderLight,
  },

  // Card styles
  card: {
    backgroundColor: Colors.light.surface,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },

  cardSecondary: {
    backgroundColor: Colors.light.surfaceSecondary,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
  },

  // Button styles
  primaryButton: {
    backgroundColor: Colors.light.tint,
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },

  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.semibold,
    fontFamily: Typography.fontFamily.semibold,
  },

  secondaryButton: {
    backgroundColor: Colors.light.tintSecondary,
    paddingVertical: Spacing.lg,
    paddingHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },

  secondaryButtonText: {
    color: Colors.light.tint,
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.semibold,
    fontFamily: Typography.fontFamily.semibold,
  },

  // Input styles
  input: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: BorderRadius.md,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    fontSize: Typography.fontSizes.base,
    backgroundColor: Colors.light.surface,
    fontFamily: Typography.fontFamily.regular,
  },

  inputFocused: {
    borderColor: Colors.light.tint,
  },

  // Text styles
  title: {
    fontSize: Typography.fontSizes['2xl'],
    fontWeight: Typography.fontWeights.bold,
    color: Colors.light.text,
    lineHeight: Typography.fontSizes['2xl'] * Typography.lineHeights.tight,
    fontFamily: Typography.fontFamily.bold,
  },

  subtitle: {
    fontSize: Typography.fontSizes.lg,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.light.text,
    lineHeight: Typography.fontSizes.lg * Typography.lineHeights.normal,
    fontFamily: Typography.fontFamily.semibold,
  },

  body: {
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.normal,
    color: Colors.light.text,
    lineHeight: Typography.fontSizes.base * Typography.lineHeights.normal,
    fontFamily: Typography.fontFamily.regular,
  },

  bodySecondary: {
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.normal,
    color: Colors.light.textSecondary,
    lineHeight: Typography.fontSizes.base * Typography.lineHeights.normal,
    fontFamily: Typography.fontFamily.regular,
  },

  caption: {
    fontSize: Typography.fontSizes.sm,
    fontWeight: Typography.fontWeights.normal,
    color: Colors.light.textSecondary,
    lineHeight: Typography.fontSizes.sm * Typography.lineHeights.normal,
    fontFamily: Typography.fontFamily.regular,
  },

  // Layout styles
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  rowBetween: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  column: {
    flexDirection: 'column',
  },

  center: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Spacing utilities
  marginBottom: {
    marginBottom: Spacing.lg,
  },

  marginTop: {
    marginTop: Spacing.lg,
  },

  paddingHorizontal: {
    paddingHorizontal: Spacing.xl,
  },

  paddingVertical: {
    paddingVertical: Spacing.lg,
  },

  // Shadow styles
  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },

  shadowLarge: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6.27,
    elevation: 10,
  },
});

/**
 * Animation configurations
 */
export const Animations = {
  spring: {
    damping: 15,
    stiffness: 150,
  },
  timing: {
    duration: 200,
  },
};