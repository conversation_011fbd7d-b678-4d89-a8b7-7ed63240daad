{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "ju<PERSON><PERSON><PERSON>", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "ju<PERSON><PERSON><PERSON>", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash-screen.jpeg", "resizeMode": "cover", "backgroundColor": "#151718"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.jujafresh.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#151718"}, "package": "com.jujafresh.app"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}}}