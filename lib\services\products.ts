import { ID } from 'appwrite';
import { appwriteConfig, databases, Query } from '../appwrite';

export interface Product {
  $id: string;
  name: string;
  description: string;
  price: number;
  unit: string;
  category: string;
  image: string;
  inStock: boolean;
  stockQuantity: number;
  discount?: number;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  $id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
}

class ProductService {
  // Fallback data for when database is not available
  private getFallbackProducts(): Product[] {
    return [
      {
        $id: 'fallback-1',
        name: 'Fresh Tomatoes',
        description: 'Locally grown fresh tomatoes',
        price: 150,
        unit: 'kg',
        category: 'vegetables',
        image: 'https://images.unsplash.com/photo-1546470427-e5ac89c8ba3b?w=400',
        inStock: true,
        stockQuantity: 50,
        featured: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        $id: 'fallback-2',
        name: 'Fresh Bananas',
        description: 'Sweet ripe bananas',
        price: 80,
        unit: 'bunch',
        category: 'fruits',
        image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',
        inStock: true,
        stockQuantity: 30,
        featured: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];
  }

  private getFallbackCategories(): Category[] {
    return [
      {
        $id: 'fallback-cat-1',
        name: 'Vegetables',
        icon: 'leaf.fill',
        color: '#4CAF50',
        description: 'Fresh vegetables',
      },
      {
        $id: 'fallback-cat-2',
        name: 'Fruits',
        icon: 'apple.logo',
        color: '#FF9800',
        description: 'Fresh fruits',
      },
    ];
  }

  // Get all products
  async getAllProducts(): Promise<Product[]> {
    try {
      const products = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.productsCollectionId,
        [Query.equal('inStock', true), Query.orderDesc('$createdAt')]
      );
      return products.documents as unknown as Product[];
    } catch (error) {
      console.error('Error fetching products:', error);
      console.log('Using fallback products data');
      return this.getFallbackProducts();
    }
  }

  // Get featured products
  async getFeaturedProducts(): Promise<Product[]> {
    try {
      const products = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.productsCollectionId,
        [
          Query.equal('featured', true),
          Query.equal('inStock', true),
          Query.limit(10)
        ]
      );
      return products.documents as unknown as Product[];
    } catch (error) {
      console.error('Error fetching featured products:', error);
      console.log('Using fallback featured products data');
      return this.getFallbackProducts();
    }
  }

  // Get product by ID
  async getProductById(productId: string): Promise<Product> {
    try {
      const product = await databases.getDocument(
        appwriteConfig.databaseId,
        appwriteConfig.productsCollectionId,
        productId
      );
      return product as unknown as Product;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  }

  // Search products
  async searchProducts(query: string): Promise<Product[]> {
    try {
      const products = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.productsCollectionId,
        [
          Query.search('name', query),
          Query.equal('inStock', true)
        ]
      );
      return products.documents as unknown as Product[];
    } catch (error) {
      console.error('Error searching products:', error);
      throw error;
    }
  }

  // Get products by category
  async getProductsByCategory(categoryId: string): Promise<Product[]> {
    try {
      const products = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.productsCollectionId,
        [
          Query.equal('category', categoryId),
          Query.equal('inStock', true)
        ]
      );
      return products.documents as unknown as Product[];
    } catch (error) {
      console.error('Error fetching products by category:', error);
      throw error;
    }
  }

  // Get all categories
  async getCategories(): Promise<Category[]> {
    try {
      const categories = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.categoriesCollectionId
      );
      return categories.documents as unknown as Category[];
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }

  // Create product (admin function)
  async createProduct(productData: Omit<Product, '$id' | 'createdAt' | 'updatedAt'>): Promise<Product> {
    try {
      const product = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.productsCollectionId,
        ID.unique(),
        {
          ...productData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );
      return product as unknown as Product;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  // Update product stock
  async updateProductStock(productId: string, quantity: number): Promise<Product> {
    try {
      const product = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.productsCollectionId,
        productId,
        {
          stockQuantity: quantity,
          inStock: quantity > 0,
          updatedAt: new Date().toISOString(),
        }
      );
      return product as unknown as Product;
    } catch (error) {
      console.error('Error updating product stock:', error);
      throw error;
    }
  }
}

export default new ProductService();
