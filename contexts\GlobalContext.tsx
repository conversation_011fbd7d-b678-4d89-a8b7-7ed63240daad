import authService, { User } from '@/lib/services/auth';
import cartService, { CartItem } from '@/lib/services/cart';
import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';

interface GlobalContextType {
  // Auth state
  user: User | null;
  isLoading: boolean;
  isLoggedIn: boolean;
  
  // Cart state
  cartItems: CartItem[];
  cartCount: number;
  cartTotal: number;
  
  // Auth methods
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string, phone?: string) => Promise<void>;
  signOut: () => Promise<void>;
  
  // Cart methods
  addToCart: (productId: string, productName: string, productPrice: number, productImage: string, productUnit: string, quantity?: number) => Promise<void>;
  updateCartQuantity: (cartItemId: string, quantity: number) => Promise<void>;
  removeFromCart: (cartItemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
}

const GlobalContext = createContext<GlobalContextType | undefined>(undefined);

export const useGlobalContext = () => {
  const context = useContext(GlobalContext);
  if (context === undefined) {
    throw new Error('useGlobalContext must be used within a GlobalProvider');
  }
  return context;
};

export const GlobalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);

  // Derived state
  const isLoggedIn = !!user;
  const cartCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);
  const cartTotal = cartItems.reduce((sum, item) => sum + (item.productPrice * item.quantity), 0);

  // Initialize app - check if user is logged in
  useEffect(() => {
    checkAuthState();
  }, []);

  const refreshCart = useCallback(async () => {
    if (!user) {
      setCartItems([]);
      return;
    }

    try {
      const items = await cartService.getCartItems(user.$id);
      setCartItems(items);
    } catch (error) {
      console.error('Error refreshing cart:', error);
      setCartItems([]);
    }
  }, [user]);

  // Load cart when user changes
  useEffect(() => {
    if (user) {
      refreshCart();
    } else {
      setCartItems([]);
    }
  }, [refreshCart, user]);

  const checkAuthState = async () => {
    try {
      setIsLoading(true);
      // Add a small delay to show the splash screen
      await new Promise(resolve => setTimeout(resolve, 1000));
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Error checking auth state:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      await authService.signIn(email, password);
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, name: string, phone?: string) => {
    try {
      setIsLoading(true);
      const newUser = await authService.createAccount({ email, password, name, phone });
      await authService.signIn(email, password);
      setUser(newUser);
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      await authService.signOut();
      setUser(null);
      setCartItems([]);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const addToCart = async (
    productId: string,
    productName: string,
    productPrice: number,
    productImage: string,
    productUnit: string,
    quantity: number = 1
  ) => {
    if (!user) {
      throw new Error('Please sign in to add items to cart');
    }

    try {
      await cartService.addToCart(
        user.$id,
        productId,
        productName,
        productPrice,
        productImage,
        productUnit,
        quantity
      );
      await refreshCart();
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  };

  const updateCartQuantity = async (cartItemId: string, quantity: number) => {
    try {
      if (quantity <= 0) {
        await removeFromCart(cartItemId);
      } else {
        await cartService.updateCartItemQuantity(cartItemId, quantity);
        await refreshCart();
      }
    } catch (error) {
      console.error('Error updating cart quantity:', error);
      throw error;
    }
  };

  const removeFromCart = async (cartItemId: string) => {
    try {
      await cartService.removeFromCart(cartItemId);
      await refreshCart();
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  };

  const clearCart = async () => {
    if (!user) return;

    try {
      await cartService.clearCart(user.$id);
      setCartItems([]);
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw error;
    }
  };



  const value: GlobalContextType = {
    // Auth state
    user,
    isLoading,
    isLoggedIn,
    
    // Cart state
    cartItems,
    cartCount,
    cartTotal,
    
    // Auth methods
    signIn,
    signUp,
    signOut,
    
    // Cart methods
    addToCart,
    updateCartQuantity,
    removeFromCart,
    clearCart,
    refreshCart,
  };

  return (
    <GlobalContext.Provider value={value}>
      {children}
    </GlobalContext.Provider>
  );
};

export default GlobalProvider;