import { ID } from 'appwrite';
import { appwriteConfig, databases, Query } from '../appwrite';

export interface CartItem {
  $id: string;
  userId: string;
  productId: string;
  productName: string;
  productPrice: number;
  productImage: string;
  productUnit: string;
  quantity: number;
  createdAt: string;
  updatedAt: string;
}

class CartService {
  // Add item to cart
  async addToCart(
    userId: string,
    productId: string,
    productName: string,
    productPrice: number,
    productImage: string,
    productUnit: string,
    quantity: number = 1
  ): Promise<CartItem> {
    try {
      // Check if item already exists in cart
      const existingItems = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.cartCollectionId,
        [
          Query.equal('userId', userId),
          Query.equal('productId', productId)
        ]
      );

      if (existingItems.documents.length > 0) {
        // Update existing item quantity
        const existingItem = existingItems.documents[0];
        const updatedItem = await databases.updateDocument(
          appwriteConfig.databaseId,
          appwriteConfig.cartCollectionId,
          existingItem.$id,
          {
            quantity: existingItem.quantity + quantity,
            updatedAt: new Date().toISOString(),
          }
        );
        return updatedItem as unknown as CartItem;
      } else {
        // Create new cart item
        const cartItem = await databases.createDocument(
          appwriteConfig.databaseId,
          appwriteConfig.cartCollectionId,
          ID.unique(),
          {
            userId,
            productId,
            productName,
            productPrice,
            productImage,
            productUnit,
            quantity,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }
        );
        return cartItem as unknown as CartItem;
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  }

  // Get user's cart items
  async getCartItems(userId: string): Promise<CartItem[]> {
    try {
      const cartItems = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.cartCollectionId,
        [
          Query.equal('userId', userId),
          Query.orderDesc('$createdAt')
        ]
      );
      return cartItems.documents as unknown as CartItem[];
    } catch (error) {
      console.error('Error fetching cart items:', error);
      throw error;
    }
  }

  // Update cart item quantity
  async updateCartItemQuantity(cartItemId: string, quantity: number): Promise<CartItem> {
    try {
      if (quantity <= 0) {
        // Remove item if quantity is 0 or less
        await this.removeFromCart(cartItemId);
        throw new Error('Item removed from cart');
      }

      const updatedItem = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.cartCollectionId,
        cartItemId,
        {
          quantity,
          updatedAt: new Date().toISOString(),
        }
      );
      return updatedItem as unknown as CartItem;
    } catch (error) {
      console.error('Error updating cart item quantity:', error);
      throw error;
    }
  }

  // Remove item from cart
  async removeFromCart(cartItemId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        appwriteConfig.databaseId,
        appwriteConfig.cartCollectionId,
        cartItemId
      );
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  }

  // Clear user's cart
  async clearCart(userId: string): Promise<void> {
    try {
      const cartItems = await this.getCartItems(userId);
      const deletePromises = cartItems.map(item => 
        databases.deleteDocument(
          appwriteConfig.databaseId,
          appwriteConfig.cartCollectionId,
          item.$id
        )
      );
      await Promise.all(deletePromises);
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw error;
    }
  }

  // Get cart summary
  async getCartSummary(userId: string) {
    try {
      const cartItems = await this.getCartItems(userId);
      const subtotal = cartItems.reduce((sum, item) => sum + (item.productPrice * item.quantity), 0);
      const itemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0);
      const deliveryFee = subtotal >= 1000 ? 0 : 50;
      const total = subtotal + deliveryFee;

      return {
        items: cartItems,
        subtotal,
        itemCount,
        deliveryFee,
        total,
        freeDelivery: subtotal >= 1000,
      };
    } catch (error) {
      console.error('Error getting cart summary:', error);
      throw error;
    }
  }
}

export default new CartService();
