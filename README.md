# JujaFresh - On-Demand Grocery Delivery App

A React Native/Expo app for on-demand grocery delivery in Juja, Kenya, built with Appwrite as the backend.

## Features

- 🛒 Browse and search products
- 🛍️ Add items to cart
- 👤 User authentication
- 📱 Cross-platform (iOS & Android)
- 🚚 Order tracking
- 💳 Multiple payment options

## Tech Stack

- **Frontend**: React Native with Expo
- **Backend**: Appwrite
- **Database**: Appwrite Database
- **Authentication**: Appwrite Auth
- **Storage**: Appwrite Storage

## Setup Instructions

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Appwrite account and project

### 1. <PERSON>lone and Install

```bash
git clone <repository-url>
cd jujafresh
npm install
```

### 2. Appwrite Setup

1. Create an Appwrite project at [cloud.appwrite.io](https://cloud.appwrite.io)
2. Get your Project ID and API Endpoint
3. Create an API key with the following scopes:
   - `databases.read`
   - `databases.write`
   - `collections.read`
   - `collections.write`
   - `attributes.read`
   - `attributes.write`
   - `documents.read`
   - `documents.write`

### 3. Database Setup

1. Update the API key in `scripts/setup-database.js`:
   ```javascript
   .setKey('YOUR_API_KEY_HERE'); // Replace with your actual API key
   ```

2. Run the database setup script:
   ```bash
   node scripts/setup-database.js
   ```

3. Seed the database with sample data:
   ```bash
   node scripts/seed-data.js
   ```

### 4. Configuration

The app is already configured with the correct Appwrite settings:
- **Project ID**: `689dc68900d2da847e62d`
- **Endpoint**: `https://fra.cloud.appwrite.io/v1`
- **Database ID**: `jujafresh-db`

### 5. Run the App

```bash
# Start the development server
npm start

# Run on iOS simulator
npm run ios

# Run on Android emulator
npm run android
```

## Project Structure

```
├── app/                    # App screens and navigation
│   ├── (tabs)/            # Tab navigation screens
│   ├── product/           # Product detail screens
│   └── checkout.tsx       # Checkout screen
├── components/            # Reusable components
├── constants/             # App constants and styles
├── contexts/              # React contexts (Global state)
├── hooks/                 # Custom React hooks
├── lib/                   # Services and utilities
│   ├── appwrite.ts       # Appwrite configuration
│   └── services/         # API services
└── scripts/              # Database setup scripts
```

## Key Components

### Global Context
The app uses a global context (`contexts/GlobalContext.tsx`) to manage:
- User authentication state
- Cart state and operations
- Global app state

### Services
- **Auth Service**: User authentication and management
- **Cart Service**: Cart operations (add, remove, update)
- **Product Service**: Product and category management
- **Order Service**: Order processing and tracking

### Database Collections

1. **Users**: User profiles and information
2. **Products**: Product catalog with details
3. **Categories**: Product categories
4. **Cart**: User cart items
5. **Orders**: Order history and tracking

## API Endpoints

The app uses Appwrite's REST API through the JavaScript SDK:

- **Authentication**: Sign up, sign in, sign out
- **Products**: CRUD operations for products
- **Cart**: Add, remove, update cart items
- **Orders**: Create and track orders

## Development

### Adding New Features

1. Create services in `lib/services/`
2. Add hooks in `hooks/`
3. Update global context if needed
4. Create UI components
5. Add navigation routes

### Database Schema

Run the setup script to create the following collections:
- Users (with user permissions)
- Products (public read, admin write)
- Categories (public read, admin write)
- Cart (user-specific permissions)
- Orders (user-specific permissions)

## Deployment

### Expo Build

```bash
# Build for production
expo build:android
expo build:ios
```

### Environment Variables

For production, consider using environment variables for:
- Appwrite endpoint
- Project ID
- API keys (server-side only)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please contact [<EMAIL>] or create an issue in the repository.