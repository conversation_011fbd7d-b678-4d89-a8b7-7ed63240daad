#!/usr/bin/env node

const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚀 JujaFresh Setup Script');
console.log('========================\n');

console.log('This script will help you set up your JujaFresh app with Appwrite.\n');

console.log('Before running this script, make sure you have:');
console.log('1. Created an Appwrite project at https://cloud.appwrite.io');
console.log('2. Created an API key with database permissions');
console.log('3. Your API key ready to paste\n');

rl.question('Do you have your Appwrite API key ready? (y/n): ', (answer) => {
  if (answer.toLowerCase() !== 'y') {
    console.log('\n📋 To get your API key:');
    console.log('1. Go to https://cloud.appwrite.io');
    console.log('2. Open your project: 689dc689002da847e62d');
    console.log('3. Go to Settings > API Keys');
    console.log('4. Create a new API key with these scopes:');
    console.log('   - databases.read, databases.write');
    console.log('   - collections.read, collections.write');
    console.log('   - attributes.read, attributes.write');
    console.log('   - documents.read, documents.write');
    console.log('5. Copy the API key and run this script again\n');
    rl.close();
    return;
  }

  rl.question('Enter your Appwrite API key: ', (apiKey) => {
    if (!apiKey || apiKey.trim().length === 0) {
      console.log('❌ API key cannot be empty');
      rl.close();
      return;
    }

    console.log('\n🔧 Setting up database...');

    // Update setup-database.js
    const setupDbPath = path.join(__dirname, 'setup-database.js');
    let setupDbContent = fs.readFileSync(setupDbPath, 'utf8');
    setupDbContent = setupDbContent.replace(
      '.setKey(\'YOUR_API_KEY_HERE\');',
      `.setKey('${apiKey.trim()}');`
    );
    fs.writeFileSync(setupDbPath, setupDbContent);

    // Update seed-data.js
    const seedDataPath = path.join(__dirname, 'seed-data.js');
    let seedDataContent = fs.readFileSync(seedDataPath, 'utf8');
    seedDataContent = seedDataContent.replace(
      '.setKey(\'YOUR_API_KEY_HERE\');',
      `.setKey('${apiKey.trim()}');`
    );
    fs.writeFileSync(seedDataPath, seedDataContent);

    console.log('✅ API key configured successfully');
    console.log('\n🗄️ Creating database structure...');

    // Run setup-database.js
    const { spawn } = require('child_process');
    const setupProcess = spawn('node', [setupDbPath], { stdio: 'inherit' });

    setupProcess.on('close', (code) => {
      if (code === 0) {
        console.log('\n🌱 Seeding database with sample data...');
        
        // Run seed-data.js
        const seedProcess = spawn('node', [seedDataPath], { stdio: 'inherit' });
        
        seedProcess.on('close', (seedCode) => {
          if (seedCode === 0) {
            console.log('\n🎉 Setup completed successfully!');
            console.log('\nNext steps:');
            console.log('1. Run: npm start');
            console.log('2. Open the app on your device/simulator');
            console.log('3. Create an account and start shopping!');
            console.log('\n📱 Happy coding!');
          } else {
            console.log('\n⚠️ Database seeding completed with warnings');
            console.log('Your app should still work, but some sample data might be missing');
          }
          rl.close();
        });
      } else {
        console.log('\n❌ Database setup failed');
        console.log('Please check your API key and try again');
        rl.close();
      }
    });
  });
});