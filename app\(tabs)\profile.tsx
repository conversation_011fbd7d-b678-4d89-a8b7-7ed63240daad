import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { BorderRadius, Colors, Spacing, Typography } from '@/constants/Colors';
import { useGlobalContext } from '@/contexts/GlobalContext';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Image,
    ScrollView,
    StyleSheet,
    TextInput,
    TouchableOpacity,
} from 'react-native';

export default function ProfileScreen() {
  const { user, isLoggedIn, isLoading, signIn, signUp, signOut } = useGlobalContext();
  const [isSignUp, setIsSignUp] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });

  const handleAuth = async () => {
    if (isSignUp) {
      if (formData.password !== formData.confirmPassword) {
        Alert.alert('Error', 'Passwords do not match');
        return;
      }
      if (!formData.name || !formData.email || !formData.password) {
        Alert.alert('Error', 'Please fill in all required fields');
        return;
      }
      try {
        await signUp(formData.email, formData.password, formData.name, formData.phone);
        Alert.alert('Success', 'Account created successfully!');
      } catch (error: any) {
        Alert.alert('Error', error.message || 'Failed to create account');
      }
    } else {
      if (!formData.email || !formData.password) {
        Alert.alert('Error', 'Please enter email and password');
        return;
      }
      try {
        await signIn(formData.email, formData.password);
        Alert.alert('Success', 'Signed in successfully!');
      } catch (error: any) {
        Alert.alert('Error', error.message || 'Failed to sign in');
      }
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      Alert.alert('Success', 'Signed out successfully!');
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to sign out');
    }
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <ThemedText style={styles.loadingText}>Loading...</ThemedText>
        </ThemedView>
      </ThemedView>
    );
  }

  if (isLoggedIn && user) {
    return (
      <ScrollView style={styles.container}>
        <ThemedView style={styles.header}>
          <ThemedText type="title">Profile</ThemedText>
        </ThemedView>

        <ThemedView style={styles.profileCard}>
          <ThemedView style={styles.avatarContainer}>
            <Image 
              source={require('@/assets/images/jujafresh-logo.jpeg')} 
              style={styles.avatar}
            />
          </ThemedView>
          
          <ThemedText type="subtitle" style={styles.userName}>
            {user.name}
          </ThemedText>
          <ThemedText style={styles.userEmail}>{user.email}</ThemedText>
          {user.phone && (
            <ThemedText style={styles.userPhone}>{user.phone}</ThemedText>
          )}
        </ThemedView>

        <ThemedView style={styles.menuSection}>
          <TouchableOpacity style={styles.menuItem}>
            <IconSymbol name="bag" size={24} color="#666" />
            <ThemedText style={styles.menuText}>My Orders</ThemedText>
            <IconSymbol name="chevron.right" size={16} color="#666" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <IconSymbol name="location" size={24} color="#666" />
            <ThemedText style={styles.menuText}>Delivery Address</ThemedText>
            <IconSymbol name="chevron.right" size={16} color="#666" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <IconSymbol name="creditcard" size={24} color="#666" />
            <ThemedText style={styles.menuText}>Payment Methods</ThemedText>
            <IconSymbol name="chevron.right" size={16} color="#666" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem}>
            <IconSymbol name="gear" size={24} color="#666" />
            <ThemedText style={styles.menuText}>Settings</ThemedText>
            <IconSymbol name="chevron.right" size={16} color="#666" />
          </TouchableOpacity>
        </ThemedView>

        <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
          <ThemedText style={styles.signOutText}>Sign Out</ThemedText>
        </TouchableOpacity>
      </ScrollView>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">{isSignUp ? 'Create Account' : 'Sign In'}</ThemedText>
        <ThemedText style={styles.subtitle}>
          {isSignUp 
            ? 'Join JujaFresh to start shopping' 
            : 'Welcome back to JujaFresh'
          }
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.formContainer}>
        {isSignUp && (
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Full Name *</ThemedText>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              placeholder="Enter your full name"
              autoCapitalize="words"
            />
          </ThemedView>
        )}

        <ThemedView style={styles.inputGroup}>
          <ThemedText style={styles.label}>Email *</ThemedText>
          <TextInput
            style={styles.input}
            value={formData.email}
            onChangeText={(text) => setFormData({ ...formData, email: text })}
            placeholder="Enter your email"
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </ThemedView>

        {isSignUp && (
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Phone Number</ThemedText>
            <TextInput
              style={styles.input}
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              placeholder="Enter your phone number"
              keyboardType="phone-pad"
            />
          </ThemedView>
        )}

        <ThemedView style={styles.inputGroup}>
          <ThemedText style={styles.label}>Password *</ThemedText>
          <TextInput
            style={styles.input}
            value={formData.password}
            onChangeText={(text) => setFormData({ ...formData, password: text })}
            placeholder="Enter your password"
            secureTextEntry
          />
        </ThemedView>

        {isSignUp && (
          <ThemedView style={styles.inputGroup}>
            <ThemedText style={styles.label}>Confirm Password *</ThemedText>
            <TextInput
              style={styles.input}
              value={formData.confirmPassword}
              onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
              placeholder="Confirm your password"
              secureTextEntry
            />
          </ThemedView>
        )}

        <TouchableOpacity style={styles.authButton} onPress={handleAuth}>
          <ThemedText style={styles.authButtonText}>
            {isSignUp ? 'Create Account' : 'Sign In'}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.switchButton} 
          onPress={() => setIsSignUp(!isSignUp)}
        >
          <ThemedText style={styles.switchText}>
            {isSignUp 
              ? 'Already have an account? Sign In' 
              : "Don't have an account? Sign Up"
            }
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  header: {
    paddingHorizontal: Spacing.xl,
    paddingTop: 60,
    paddingBottom: Spacing.xl,
    backgroundColor: Colors.dark.surface,
  },
  subtitle: {
    marginTop: Spacing.sm,
    color: Colors.dark.textSecondary,
    fontSize: Typography.fontSizes.base,
    fontFamily: Typography.fontFamily.regular,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: Spacing.md,
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  profileCard: {
    backgroundColor: Colors.dark.surface,
    margin: Spacing.xl,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  avatarContainer: {
    marginBottom: Spacing.lg,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  userName: {
    marginBottom: Spacing.xs,
  },
  userEmail: {
    color: Colors.dark.textSecondary,
    marginBottom: Spacing.xs,
    fontFamily: Typography.fontFamily.regular,
  },
  userPhone: {
    color: Colors.dark.textSecondary,
    fontFamily: Typography.fontFamily.regular,
  },
  menuSection: {
    backgroundColor: Colors.dark.surface,
    marginHorizontal: Spacing.xl,
    borderRadius: BorderRadius.lg,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.dark.border,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.dark.border,
  },
  menuText: {
    flex: 1,
    marginLeft: Spacing.md,
    fontSize: Typography.fontSizes.base,
    color: Colors.dark.text,
    fontFamily: Typography.fontFamily.medium,
  },
  signOutButton: {
    backgroundColor: '#FF5722',
    margin: Spacing.xl,
    paddingVertical: Spacing.lg,
    borderRadius: BorderRadius.lg,
    alignItems: 'center',
  },
  signOutText: {
    color: '#FFFFFF',
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.semibold,
  },
  formContainer: {
    padding: Spacing.xl,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: Typography.fontSizes.sm,
    fontWeight: Typography.fontWeights.medium,
    marginBottom: Spacing.sm,
    color: Colors.dark.text,
    fontFamily: Typography.fontFamily.medium,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.dark.border,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    fontSize: Typography.fontSizes.base,
    backgroundColor: Colors.dark.surface,
    color: Colors.dark.text,
    fontFamily: Typography.fontFamily.regular,
  },
  authButton: {
    backgroundColor: Colors.dark.success,
    paddingVertical: Spacing.lg,
    borderRadius: BorderRadius.lg,
    alignItems: 'center',
    marginTop: Spacing.lg,
  },
  authButtonText: {
    color: '#FFFFFF',
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.semibold,
  },
  switchButton: {
    alignItems: 'center',
    marginTop: Spacing.lg,
  },
  switchText: {
    color: Colors.dark.success,
    fontSize: Typography.fontSizes.sm,
    fontFamily: Typography.fontFamily.medium,
  },
});