const { Client, Databases, Storage, ID, Permission, Role } = require('appwrite');

// Initialize Appwrite client
const client = new Client();
client
  .setEndpoint('https://fra.cloud.appwrite.io/v1')
  .setProject('689dc68900d2da847e62d')
  .setKey('YOUR_API_KEY_HERE'); // You'll need to add your API key

const databases = new Databases(client);
const storage = new Storage(client);

const databaseId = 'jujafresh-db';

async function setupDatabase() {
  try {
    console.log('Setting up JujaFresh database...');

    // Create database
    try {
      await databases.create(databaseId, 'JujaFresh Database');
      console.log('✅ Database created successfully');
    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️ Database already exists');
      } else {
        throw error;
      }
    }

    // Create Users collection
    try {
      await databases.createCollection(
        databaseId,
        'users',
        'Users',
        [
          Permission.read(Role.user('ID')),
          Permission.write(Role.user('ID')),
          Permission.create(Role.users()),
          Permission.update(Role.user('ID')),
          Permission.delete(Role.user('ID'))
        ]
      );
      console.log('✅ Users collection created');

      // Add attributes to Users collection
      await databases.createStringAttribute(databaseId, 'users', 'accountId', 255, true);
      await databases.createStringAttribute(databaseId, 'users', 'name', 255, true);
      await databases.createEmailAttribute(databaseId, 'users', 'email', true);
      await databases.createStringAttribute(databaseId, 'users', 'phone', 20, false);
      await databases.createStringAttribute(databaseId, 'users', 'address', 500, false);
      await databases.createStringAttribute(databaseId, 'users', 'avatar', 255, false);
      console.log('✅ Users collection attributes added');
    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️ Users collection already exists');
      } else {
        throw error;
      }
    }

    // Create Categories collection
    try {
      await databases.createCollection(
        databaseId,
        'categories',
        'Categories',
        [
          Permission.read(Role.any()),
          Permission.create(Role.users()),
          Permission.update(Role.users()),
          Permission.delete(Role.users())
        ]
      );
      console.log('✅ Categories collection created');

      // Add attributes to Categories collection
      await databases.createStringAttribute(databaseId, 'categories', 'name', 255, true);
      await databases.createStringAttribute(databaseId, 'categories', 'icon', 255, true);
      await databases.createStringAttribute(databaseId, 'categories', 'color', 7, true);
      await databases.createStringAttribute(databaseId, 'categories', 'description', 500, false);
      console.log('✅ Categories collection attributes added');
    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️ Categories collection already exists');
      } else {
        throw error;
      }
    }

    // Create Products collection
    try {
      await databases.createCollection(
        databaseId,
        'products',
        'Products',
        [
          Permission.read(Role.any()),
          Permission.create(Role.users()),
          Permission.update(Role.users()),
          Permission.delete(Role.users())
        ]
      );
      console.log('✅ Products collection created');

      // Add attributes to Products collection
      await databases.createStringAttribute(databaseId, 'products', 'name', 255, true);
      await databases.createStringAttribute(databaseId, 'products', 'description', 1000, false);
      await databases.createFloatAttribute(databaseId, 'products', 'price', true);
      await databases.createStringAttribute(databaseId, 'products', 'unit', 50, true);
      await databases.createStringAttribute(databaseId, 'products', 'category', 255, true);
      await databases.createStringAttribute(databaseId, 'products', 'image', 255, true);
      await databases.createBooleanAttribute(databaseId, 'products', 'inStock', true);
      await databases.createIntegerAttribute(databaseId, 'products', 'stockQuantity', true);
      await databases.createFloatAttribute(databaseId, 'products', 'discount', false);
      await databases.createBooleanAttribute(databaseId, 'products', 'featured', true);
      await databases.createDatetimeAttribute(databaseId, 'products', 'createdAt', true);
      await databases.createDatetimeAttribute(databaseId, 'products', 'updatedAt', true);
      console.log('✅ Products collection attributes added');
    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️ Products collection already exists');
      } else {
        throw error;
      }
    }

    // Create Cart collection
    try {
      await databases.createCollection(
        databaseId,
        'cart',
        'Cart',
        [
          Permission.read(Role.user('ID')),
          Permission.write(Role.user('ID')),
          Permission.create(Role.users()),
          Permission.update(Role.user('ID')),
          Permission.delete(Role.user('ID'))
        ]
      );
      console.log('✅ Cart collection created');

      // Add attributes to Cart collection
      await databases.createStringAttribute(databaseId, 'cart', 'userId', 255, true);
      await databases.createStringAttribute(databaseId, 'cart', 'productId', 255, true);
      await databases.createStringAttribute(databaseId, 'cart', 'productName', 255, true);
      await databases.createFloatAttribute(databaseId, 'cart', 'productPrice', true);
      await databases.createStringAttribute(databaseId, 'cart', 'productImage', 255, true);
      await databases.createStringAttribute(databaseId, 'cart', 'productUnit', 50, true);
      await databases.createIntegerAttribute(databaseId, 'cart', 'quantity', true);
      await databases.createDatetimeAttribute(databaseId, 'cart', 'createdAt', true);
      await databases.createDatetimeAttribute(databaseId, 'cart', 'updatedAt', true);
      console.log('✅ Cart collection attributes added');
    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️ Cart collection already exists');
      } else {
        throw error;
      }
    }

    // Create Orders collection
    try {
      await databases.createCollection(
        databaseId,
        'orders',
        'Orders',
        [
          Permission.read(Role.user('ID')),
          Permission.write(Role.user('ID')),
          Permission.create(Role.users()),
          Permission.update(Role.user('ID')),
          Permission.delete(Role.user('ID'))
        ]
      );
      console.log('✅ Orders collection created');

      // Add attributes to Orders collection
      await databases.createStringAttribute(databaseId, 'orders', 'userId', 255, true);
      await databases.createStringAttribute(databaseId, 'orders', 'orderNumber', 255, true);
      await databases.createStringAttribute(databaseId, 'orders', 'items', 10000, true); // JSON string
      await databases.createFloatAttribute(databaseId, 'orders', 'subtotal', true);
      await databases.createFloatAttribute(databaseId, 'orders', 'deliveryFee', true);
      await databases.createFloatAttribute(databaseId, 'orders', 'total', true);
      await databases.createEnumAttribute(databaseId, 'orders', 'status', ['pending', 'confirmed', 'preparing', 'out_for_delivery', 'delivered', 'cancelled'], true);
      await databases.createEnumAttribute(databaseId, 'orders', 'paymentMethod', ['cash_on_delivery', 'mpesa', 'card'], true);
      await databases.createEnumAttribute(databaseId, 'orders', 'paymentStatus', ['pending', 'paid', 'failed'], true);
      await databases.createStringAttribute(databaseId, 'orders', 'deliveryAddress', 2000, true); // JSON string
      await databases.createStringAttribute(databaseId, 'orders', 'customerNotes', 500, false);
      await databases.createDatetimeAttribute(databaseId, 'orders', 'estimatedDeliveryTime', false);
      await databases.createDatetimeAttribute(databaseId, 'orders', 'createdAt', true);
      await databases.createDatetimeAttribute(databaseId, 'orders', 'updatedAt', true);
      console.log('✅ Orders collection attributes added');
    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️ Orders collection already exists');
      } else {
        throw error;
      }
    }

    // Create Storage bucket for product images
    try {
      await storage.createBucket(
        'product-images',
        'Product Images',
        [
          Permission.read(Role.any()),
          Permission.create(Role.users()),
          Permission.update(Role.users()),
          Permission.delete(Role.users())
        ]
      );
      console.log('✅ Product images storage bucket created');
    } catch (error) {
      if (error.code === 409) {
        console.log('ℹ️ Product images storage bucket already exists');
      } else {
        throw error;
      }
    }

    console.log('🎉 Database setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Add your API key to this script');
    console.log('2. Run: node scripts/seed-data.js to add sample data');
    console.log('3. Update your app configuration if needed');

  } catch (error) {
    console.error('❌ Error setting up database:', error);
  }
}

setupDatabase();