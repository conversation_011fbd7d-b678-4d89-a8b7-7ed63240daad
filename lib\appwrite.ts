import { Account, Client, Databases, Query, Storage } from 'appwrite';

// Get environment variables with fallbacks
const getEnvVar = (key: string, fallback: string): string => {
  const value = process.env[key];
  if (!value) {
    console.warn(`Environment variable ${key} not found, using fallback: ${fallback}`);
    return fallback;
  }
  return value;
};

// Appwrite configuration
export const appwriteConfig = {
  endpoint: getEnvVar('EXPO_PUBLIC_APPWRITE_ENDPOINT', 'https://fra.cloud.appwrite.io/v1'),
  projectId: getEnvVar('EXPO_PUBLIC_APPWRITE_PROJECT_ID', '689dc68900d2da847e62d'),
  databaseId: getEnvVar('EXPO_PUBLIC_APPWRITE_DATABASE_ID', 'jujafresh-db'),
  // Collections
  usersCollectionId: getEnvVar('EXPO_PUBLIC_USERS_COLLECTION_ID', 'users'),
  productsCollectionId: getEnvVar('EXPO_PUBLIC_PRODUCTS_COLLECTION_ID', 'products'),
  categoriesCollectionId: getEnvVar('EXPO_PUBLIC_CATEGORIES_COLLECTION_ID', 'categories'),
  ordersCollectionId: getEnvVar('EXPO_PUBLIC_ORDERS_COLLECTION_ID', 'orders'),
  cartCollectionId: getEnvVar('EXPO_PUBLIC_CART_COLLECTION_ID', 'cart'),
  // Storage
  storageId: getEnvVar('EXPO_PUBLIC_APPWRITE_STORAGE_ID', 'product-images'),
};

// Validate configuration
const validateConfig = () => {
  const requiredFields = ['endpoint', 'projectId', 'databaseId'];
  const missingFields = requiredFields.filter(field => !appwriteConfig[field as keyof typeof appwriteConfig]);
  
  if (missingFields.length > 0) {
    throw new Error(`Missing required Appwrite configuration: ${missingFields.join(', ')}`);
  }
};

// Initialize Appwrite client
const client = new Client();

try {
  validateConfig();
  client
    .setEndpoint(appwriteConfig.endpoint)
    .setProject(appwriteConfig.projectId);
  
  console.log('✅ Appwrite client initialized successfully');
  console.log(`📡 Endpoint: ${appwriteConfig.endpoint}`);
  console.log(`🏗️ Project ID: ${appwriteConfig.projectId}`);
} catch (error) {
  console.error('❌ Failed to initialize Appwrite client:', error);
  throw error;
}

// Initialize services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);

export { Query };
export default client;