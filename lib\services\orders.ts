import { ID } from 'appwrite';
import { appwriteConfig, databases, Query } from '../appwrite';
import { CartItem } from './cart';

export interface Order {
  $id: string;
  userId: string;
  orderNumber: string;
  items: OrderItem[];
  subtotal: number;
  deliveryFee: number;
  total: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'out_for_delivery' | 'delivered' | 'cancelled';
  paymentMethod: 'cash_on_delivery' | 'mpesa' | 'card';
  paymentStatus: 'pending' | 'paid' | 'failed';
  deliveryAddress: DeliveryAddress;
  customerNotes?: string;
  estimatedDeliveryTime?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: string;
  productName: string;
  productPrice: number;
  productImage: string;
  productUnit: string;
  quantity: number;
}

export interface DeliveryAddress {
  fullName: string;
  phone: string;
  address: string;
  landmark?: string;
  instructions?: string;
}

export interface CreateOrderData {
  userId: string;
  items: CartItem[];
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: Order['paymentMethod'];
  deliveryAddress: DeliveryAddress;
  customerNotes?: string;
}

class OrderService {
  // Create a new order
  async createOrder(orderData: CreateOrderData): Promise<Order> {
    try {
      const orderNumber = this.generateOrderNumber();
      
      const order = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        ID.unique(),
        {
          userId: orderData.userId,
          orderNumber,
          items: orderData.items.map(item => ({
            productId: item.productId,
            productName: item.productName,
            productPrice: item.productPrice,
            productImage: item.productImage,
            productUnit: item.productUnit,
            quantity: item.quantity,
          })),
          subtotal: orderData.subtotal,
          deliveryFee: orderData.deliveryFee,
          total: orderData.total,
          status: 'pending',
          paymentMethod: orderData.paymentMethod,
          paymentStatus: orderData.paymentMethod === 'cash_on_delivery' ? 'pending' : 'pending',
          deliveryAddress: orderData.deliveryAddress,
          customerNotes: orderData.customerNotes || '',
          estimatedDeliveryTime: this.calculateEstimatedDeliveryTime(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      );

      return order as unknown as Order;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  // Get user's orders
  async getUserOrders(userId: string): Promise<Order[]> {
    try {
      const orders = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        [
          Query.equal('userId', userId),
          Query.orderDesc('$createdAt')
        ]
      );
      return orders.documents as unknown as Order[];
    } catch (error) {
      console.error('Error fetching user orders:', error);
      throw error;
    }
  }

  // Get order by ID
  async getOrderById(orderId: string): Promise<Order> {
    try {
      const order = await databases.getDocument(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        orderId
      );
      return order as unknown as Order;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  // Update order status
  async updateOrderStatus(orderId: string, status: Order['status']): Promise<Order> {
    try {
      const updatedOrder = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        orderId,
        {
          status,
          updatedAt: new Date().toISOString(),
        }
      );
      return updatedOrder as unknown as Order;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  // Update payment status
  async updatePaymentStatus(orderId: string, paymentStatus: Order['paymentStatus']): Promise<Order> {
    try {
      const updatedOrder = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        orderId,
        {
          paymentStatus,
          updatedAt: new Date().toISOString(),
        }
      );
      return updatedOrder as unknown as Order;
    } catch (error) {
      console.error('Error updating payment status:', error);
      throw error;
    }
  }

  // Cancel order
  async cancelOrder(orderId: string): Promise<Order> {
    try {
      const updatedOrder = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        orderId,
        {
          status: 'cancelled',
          updatedAt: new Date().toISOString(),
        }
      );
      return updatedOrder as unknown as Order;
    } catch (error) {
      console.error('Error cancelling order:', error);
      throw error;
    }
  }

  // Generate order number
  private generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `JF${timestamp.slice(-6)}${random}`;
  }

  // Calculate estimated delivery time
  private calculateEstimatedDeliveryTime(): string {
    const now = new Date();
    const deliveryTime = new Date(now.getTime() + (2 * 60 * 60 * 1000)); // 2 hours from now
    return deliveryTime.toISOString();
  }

  // Get order status display text
  getOrderStatusText(status: Order['status']): string {
    const statusMap = {
      pending: 'Order Placed',
      confirmed: 'Order Confirmed',
      preparing: 'Preparing Order',
      out_for_delivery: 'Out for Delivery',
      delivered: 'Delivered',
      cancelled: 'Cancelled',
    };
    return statusMap[status] || 'Unknown Status';
  }

  // Get payment status display text
  getPaymentStatusText(status: Order['paymentStatus']): string {
    const statusMap = {
      pending: 'Payment Pending',
      paid: 'Payment Completed',
      failed: 'Payment Failed',
    };
    return statusMap[status] || 'Unknown Status';
  }
}

export default new OrderService();
